{
	"compilerOptions": {
		"target": "es2024",
		"lib": [
			"ES2024"
		],
		"module": "nodenext",
		"alwaysStrict": true,
		"removeComments": false,
		"preserveConstEnums": true,
		"sourceMap": true,
		"resolveJsonModule": true,
		// enable JavaScript type checking for the language service
		// use the tsconfig.build.json for compiling which disable JavaScript
		// type checking so that JavaScript file are not transpiled
		"allowJs": true,
		"strict": true,
		"exactOptionalPropertyTypes": false,
		"useUnknownInCatchVariables": false,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"newLine": "lf",
		"noEmit": true
	},
	"include": [
		"**/*.ts",
		"**/*.js"
	],
	"exclude": [
		"node_modules/**"
	]
}
