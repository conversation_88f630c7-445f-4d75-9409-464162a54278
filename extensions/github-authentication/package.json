{"name": "github-authentication", "displayName": "%displayName%", "description": "%description%", "publisher": "vscode", "license": "MIT", "version": "0.0.2", "engines": {"vscode": "^1.41.0"}, "icon": "images/icon.png", "categories": ["Other"], "api": "none", "extensionKind": ["ui", "workspace"], "enabledApiProposals": ["authIssuers", "authProviderSpecific"], "activationEvents": [], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": "limited", "restrictedConfigurations": ["github-enterprise.uri"]}}, "contributes": {"authentication": [{"label": "GitHub", "id": "github", "authorizationServerGlobs": ["https://github.com/login/oauth"]}, {"label": "GitHub Enterprise Server", "id": "github-enterprise", "authorizationServerGlobs": ["*"]}], "configuration": [{"title": "%config.github-enterprise.title%", "properties": {"github-enterprise.uri": {"type": "string", "markdownDescription": "%config.github-enterprise.uri.description%", "pattern": "^(?:$|(https?)://(?!github\\.com).*)"}}}]}, "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "main": "./out/extension.js", "browser": "./dist/browser/extension.js", "scripts": {"compile": "gulp compile-extension:github-authentication", "compile-web": "npx webpack-cli --config extension-browser.webpack.config --mode none", "watch": "gulp watch-extension:github-authentication", "watch-web": "npx webpack-cli --config extension-browser.webpack.config --mode none --watch --info-verbosity verbose", "vscode:prepublish": "npm run compile"}, "dependencies": {"node-fetch": "2.6.7", "@vscode/extension-telemetry": "^0.9.8", "vscode-tas-client": "^0.1.84"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "22.x", "@types/node-fetch": "^2.5.7"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}