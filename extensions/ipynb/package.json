{"name": "ipynb", "displayName": "%displayName%", "description": "%description%", "publisher": "vscode", "version": "1.0.0", "license": "MIT", "icon": "media/icon.png", "engines": {"vscode": "^1.57.0"}, "enabledApiProposals": ["diffContentOptions"], "activationEvents": ["onNotebook:jupyter-notebook", "onNotebookSerializer:interactive", "onNotebookSerializer:repl"], "extensionKind": ["workspace", "ui"], "main": "./out/ipynbMain.node.js", "browser": "./dist/browser/ipynbMain.browser.js", "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"configuration": [{"properties": {"ipynb.pasteImagesAsAttachments.enabled": {"type": "boolean", "scope": "resource", "markdownDescription": "%ipynb.pasteImagesAsAttachments.enabled%", "default": true}, "ipynb.experimental.serialization": {"type": "boolean", "scope": "resource", "markdownDescription": "%ipynb.experimental.serialization%", "default": true, "tags": ["experimental"]}}}], "commands": [{"command": "ipynb.newUntitledIpynb", "title": "%newUntitledIpynb.title%", "shortTitle": "%newUntitledIpynb.shortTitle%", "category": "Create"}, {"command": "ipynb.openIpynbInNotebookEditor", "title": "%openIpynbInNotebookEditor.title%"}, {"command": "ipynb.cleanInvalidImageAttachment", "title": "%cleanInvalidImageAttachment.title%"}, {"command": "notebook.cellOutput.copy", "title": "%copyCellOutput.title%", "category": "Notebook"}, {"command": "notebook.cellOutput.addToChat", "title": "%addCellOutputToChat.title%", "category": "Notebook", "enablement": "chatIsEnabled"}, {"command": "notebook.cellOutput.openInTextEditor", "title": "%openCellOutput.title%", "category": "Notebook"}], "notebooks": [{"type": "jupyter-notebook", "displayName": "Jupyter Notebook", "selector": [{"filenamePattern": "*.ipynb"}], "priority": "default"}], "notebookRenderer": [{"id": "vscode.markdown-it-cell-attachment-renderer", "displayName": "%markdownAttachmentRenderer.displayName%", "entrypoint": {"extends": "vscode.markdown-it-renderer", "path": "./notebook-out/cellAttachmentRenderer.js"}}], "menus": {"file/newFile": [{"command": "ipynb.newUntitledIpynb", "group": "notebook"}], "commandPalette": [{"command": "ipynb.newUntitledIpynb"}, {"command": "ipynb.openIpynbInNotebookEditor", "when": "false"}, {"command": "ipynb.cleanInvalidImageAttachment", "when": "false"}, {"command": "notebook.cellOutput.copy", "when": "notebookCellHasOutputs"}, {"command": "notebook.cellOutput.openInTextEditor", "when": "false"}], "webview/context": [{"command": "notebook.cellOutput.copy", "when": "webviewId == 'notebook.output' && webviewSection == 'image'", "group": "context@1"}, {"command": "notebook.cellOutput.copy", "when": "webviewId == 'notebook.output' && webviewSection == 'text'"}, {"command": "notebook.cellOutput.addToChat", "when": "webviewId == 'notebook.output' && (webviewSection == 'text' || webviewSection == 'image')", "group": "context@2"}, {"command": "notebook.cellOutput.openInTextEditor", "when": "webviewId == 'notebook.output' && webviewSection == 'text'"}]}}, "scripts": {"compile": "npx gulp compile-extension:ipynb && npm run build-notebook", "watch": "npx gulp watch-extension:ipynb", "build-notebook": "node ./esbuild.mjs"}, "dependencies": {"@enonic/fnv-plus": "^1.3.0", "detect-indent": "^6.0.0"}, "devDependencies": {"@jupyterlab/nbformat": "^3.2.9", "@types/markdown-it": "12.2.3"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}